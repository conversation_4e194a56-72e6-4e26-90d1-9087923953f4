<template>
  <div id="app">
    <OfflineIndicator />
    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
    <CentralModal />
    <FlashMessages />
  </div>
</template>

<script>
import CentralModal from './components/shared/CentralModal.vue'
import FlashMessages from './components/FlashMessages.vue'
import OfflineIndicator from './components/OfflineIndicator.vue'

export default {
  name: 'App',
  components: {
    CentralModal,
    FlashMessages,
    OfflineIndicator
  }
}
</script>

<style>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>