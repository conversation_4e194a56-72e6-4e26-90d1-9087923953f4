// ABOUTME: PWA Service Worker - Phase 1 & 2 implementation with JWT support and offline capabilities
// ABOUTME: Handles service worker registration, caching strategies, and offline API request queuing

const CACHE_NAME = 'tymbox-v1';
const STATIC_CACHE_NAME = 'tymbox-static-v1';
const API_CACHE_NAME = 'tymbox-api-v1';

// Assets to cache for offline use
// TYM-83 FIX: Remove '/' as it may fail in development
const STATIC_ASSETS = [
  '/offline.html',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
];

// SECURITY FIX (TYM-78): Removed API endpoint caching to prevent sensitive data exposure
// All API requests now use network-only strategy for security

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('[SW] Install event');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('[SW] Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('[SW] Static assets cached');
        // Skip waiting removed - let new SW wait for all clients to close
      })
      .catch((error) => {
        console.error('[SW] Failed to cache static assets:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activate event');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            // SECURITY FIX (TYM-85): Delete all caches except current static cache
            // API cache is no longer used (network-only strategy) so delete old API caches
            if (cacheName !== STATIC_CACHE_NAME) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[SW] Cache cleanup complete');
        // TYM-83 FIX: Remove clients.claim() to prevent reload loops
        // Service worker will take control on next navigation/reload naturally
        // return self.clients.claim();
      })
  );
});

// Fetch event - handle network requests
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // TYM-83 FIX: Do NOT intercept API requests - let axios interceptor handle authentication
  // This allows token refresh mechanism to work properly for forever login
  // IMPORTANT: API requests bypass service worker completely - no offline queuing
  // All API requests fail immediately when offline (handled by frontend)
  
  // Handle static assets only - NEVER cache document requests to prevent reload loops
  if (request.destination === 'script' || 
      request.destination === 'style' ||
      request.destination === 'image') {
    event.respondWith(handleStaticRequest(request));
    return;
  }
  
  // For document requests, return 503 to let SPA handle offline state
  // This prevents users being kicked out of the Vue SPA to static HTML
  if (request.destination === 'document') {
    event.respondWith(
      fetch(request).catch(() => {
        return new Response('Network Error', { 
          status: 503,
          statusText: 'Service Unavailable'
        });
      })
    );
    return;
  }
  
  // Default: try network first (for other non-API, non-static requests)
  event.respondWith(
    fetch(request).catch(() => {
      return new Response('Network Error', { status: 503 });
    })
  );
});

// DEPRECATED: API request handler - NO LONGER USED
// This function is preserved for historical reference but is NEVER CALLED
// API requests bypass the service worker entirely per TYM-83 fix
// The axios interceptor handles all API authentication and error handling
// 
// IMPORTANT: API request offline queuing is NOT IMPLEMENTED
// All API requests fail immediately when offline for security reasons
// Future implementation would require careful security consideration
//
// async function handleApiRequest(request) {
//   // This code is never executed - see fetch event handler above
// }

// Handle static asset requests
async function handleStaticRequest(request) {
  try {
    // Try cache first for static assets
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Try network
    const networkResponse = await fetch(request);
    
    // Cache the response
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE_NAME);
      cache.put(request.clone(), networkResponse.clone());
    }
    
    return networkResponse;
    
  } catch (error) {
    console.log('[SW] Failed to fetch static asset:', error);
    
    // Try cache again
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return 503 for document requests to let SPA handle offline state
    if (request.destination === 'document') {
      return new Response('Network Error', { 
        status: 503,
        statusText: 'Service Unavailable'
      });
    }
    
    return new Response('Network Error', { status: 503 });
  }
}

// SECURITY FIX (TYM-81): Removed isValidJWT() function 
// Client-side JWT validation creates token forgery vulnerability
// JWT validation must ONLY happen on the backend server for security

// SECURITY FIX (TYM-78): Removed isCacheableApiEndpoint function
// All API requests now use network-only strategy - no caching for security

// DEPRECATED: Offline request queuing - NO LONGER USED
// This function is preserved for historical reference but is NEVER CALLED
// API requests bypass the service worker entirely, so offline queuing doesn't happen
// 
// IMPORTANT: Offline API request queuing is NOT IMPLEMENTED
// All API requests fail immediately when offline for security reasons
// Future implementation would require:
// - Proper security for storing sensitive data
// - Token refresh handling for queued requests
// - Conflict resolution for outdated requests
//
// async function queueOfflineRequest(request) {
//   // This code is never executed - API requests bypass service worker
// }

// Message handling for client communication
self.addEventListener('message', (event) => {
  console.log('[SW] Message received:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }
  
  // CRITICAL: Clear API caches on logout to prevent serving stale authenticated data
  if (event.data && event.data.type === 'CLEAR_API_CACHES') {
    console.log('[SW] Received CLEAR_API_CACHES message. Clearing API caches...');
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames
          .filter((name) => name === API_CACHE_NAME || name.includes('api'))
          .map((name) => {
            console.log('[SW] Deleting cache:', name);
            return caches.delete(name);
          })
      );
    }).then(() => {
      console.log('[SW] API caches cleared successfully');
    }).catch((error) => {
      console.error('[SW] Failed to clear API caches:', error);
    });
  }
});

console.log('[SW] Service Worker loaded successfully');